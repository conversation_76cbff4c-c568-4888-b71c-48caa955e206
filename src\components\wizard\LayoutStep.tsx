'use client';
import React from 'react';
import { LayoutType, ColorScheme, colorSchemes } from './types';
import { useInvoiceStore } from '../../store/invoiceStore';

const InvoicePreview = React.lazy(() => import('../InvoicePreview'));

// Static preview components to avoid loading issues
function getLayoutPreview(layout: LayoutType) {
  const previews = {
    classic: (
      <div className="w-full h-full bg-white p-2 text-xs">
        <div className="border-b border-gray-300 pb-1 mb-1">
          <div className="font-bold text-blue-600">INVOICE</div>
          <div className="text-gray-600">Classic Layout</div>
        </div>
        <div className="space-y-1">
          <div className="h-1 bg-gray-200 rounded"></div>
          <div className="h-1 bg-gray-200 rounded w-3/4"></div>
          <div className="h-1 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    ),
    modern: (
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-purple-50 p-2 text-xs">
        <div className="font-bold text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text mb-1">
          INVOICE
        </div>
        <div className="space-y-1">
          <div className="h-1 bg-gradient-to-r from-blue-200 to-purple-200 rounded"></div>
          <div className="h-1 bg-gradient-to-r from-blue-200 to-purple-200 rounded w-3/4"></div>
          <div className="h-1 bg-gradient-to-r from-blue-200 to-purple-200 rounded w-1/2"></div>
        </div>
      </div>
    ),
    minimal: (
      <div className="w-full h-full bg-white p-2 text-xs font-mono">
        <div className="border-b border-gray-400 pb-1 mb-1">
          <div className="font-bold text-gray-800">INVOICE</div>
          <div className="text-gray-500">Minimal Design</div>
        </div>
        <div className="space-y-1">
          <div className="h-1 bg-gray-300 rounded"></div>
          <div className="h-1 bg-gray-300 rounded w-3/4"></div>
          <div className="h-1 bg-gray-300 rounded w-1/2"></div>
        </div>
      </div>
    )
  };

  return previews[layout] || previews.classic;
}

interface Props {
  selected: LayoutType | null;
  selectedColor?: ColorScheme;
  onSelect: (layout: LayoutType, colorScheme?: ColorScheme) => void;
  onProceed?: () => void;
}

export default function LayoutStep({ selected, selectedColor, onSelect, onProceed }: Props) {
  const { data } = useInvoiceStore();

  const handleColorSelect = (colorScheme: ColorScheme) => {
    if (selected) {
      onSelect(selected, colorScheme);
    }
  };

  return (
    <section className="w-full max-w-4xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-6 py-8 sm:px-10 sm:py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Pick your style</h2>

      {/* Layout Selection */}
      <div className="flex flex-wrap gap-4 justify-center mb-8">
        {(['classic', 'modern', 'minimal'] as LayoutType[]).map((layout) => (
          <button
            key={layout}
            className={`border-2 rounded-xl p-4 transition-all shadow-sm hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-400 ${
              selected === layout ? 'border-indigo-600' : 'border-gray-200'
            } w-36 sm:w-44 bg-white/60 dark:bg-gray-700/30 backdrop-blur`}
            onClick={() => onSelect(layout)}
          >
            <span className="block text-lg font-bold capitalize mb-2">{layout}</span>
            <div className="w-32 h-24 sm:w-40 sm:h-28 overflow-hidden rounded mb-1 bg-white dark:bg-gray-800 border border-gray-200">
              {getLayoutPreview(layout)}
            </div>
            <span className="text-xs text-gray-400">Preview</span>
          </button>
        ))}
      </div>

      {/* Color Selection - Only show if layout is selected */}
      {selected && (
        <>
          <h3 className="text-xl font-semibold mb-4 text-center text-indigo-700">Choose your color</h3>
          <div className="flex flex-wrap gap-3 justify-center mb-6">
            {(Object.keys(colorSchemes) as ColorScheme[]).map((colorScheme) => {
              const colors = colorSchemes[colorScheme];
              return (
                <button
                  key={colorScheme}
                  className={`border-2 rounded-lg p-3 transition-all shadow-sm hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-400 ${
                    selectedColor === colorScheme ? 'border-indigo-600' : 'border-gray-200'
                  } bg-white/60 dark:bg-gray-700/30 backdrop-blur min-w-[100px]`}
                  onClick={() => handleColorSelect(colorScheme)}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <div
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: colors.primary }}
                    />
                    <div
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: colors.secondary }}
                    />
                    <div
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: colors.accent }}
                    />
                  </div>
                  <span className="block text-sm font-medium capitalize">{colors.name}</span>
                </button>
              );
            })}
          </div>

          {/* Full Preview - Only show if both layout and color are selected */}
          {selectedColor && (
            <div className="mt-8 w-full max-w-2xl">
              <h4 className="text-lg font-semibold mb-4 text-center text-indigo-700">Preview with your data</h4>
              <div className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
                <React.Suspense fallback={<div className="w-full h-64 bg-gradient-to-br from-gray-100 to-gray-300 flex items-center justify-center text-sm text-gray-500">Loading preview...</div>}>
                  <div className="transform scale-50 origin-top-left" style={{ width: '200%', height: '200%' }}>
                    <InvoicePreview data={data} layout={selected} />
                  </div>
                </React.Suspense>
              </div>
            </div>
          )}

          {/* Proceed Button - Only show if both layout and color are selected */}
          {selectedColor && onProceed && (
            <button
              className="px-6 py-3 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition mt-6"
              onClick={onProceed}
            >
              Continue to Preview
            </button>
          )}
        </>
      )}
    </section>
  );
}

