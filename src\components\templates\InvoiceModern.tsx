import React from "react";
import type { Service } from "../../store/invoiceStore";
import { InvoiceData } from "../../store/invoiceStore";
import { colorSchemes, ColorScheme } from "../wizard/types";
import Image from "next/image";

type Props = {
  data: InvoiceData;
};

export default function InvoiceModern({ data }: Props) {
  // Get color scheme from data, default to 'blue' if not specified
  const selectedColorScheme = (data.colorScheme as ColorScheme) || 'blue';
  const colors = colorSchemes[selectedColorScheme];

  // Calculate total before VAT
  const subtotal = data.services.reduce((sum: number, s: Service) => sum + s.price, 0);
  // Calculate VAT amount if applicable
  const vatAmount = data.vatPercent ? (subtotal * (data.vatPercent / 100)) : 0;
  // Calculate total including VAT
  const total = subtotal + vatAmount;
  
  return (
    <div
      className="p-12 w-full sm:w-[210mm] sm:h-[297mm] mx-auto font-sans rounded-3xl shadow-2xl flex flex-col justify-between overflow-hidden relative"
      style={{
        background: `linear-gradient(to bottom right, ${colors.accent}40, ${colors.accent}20, ${colors.accent}10)`
      }}
    >
      {/* Decorative elements */}
      <div
        className="absolute top-0 right-0 w-80 h-80 rounded-full opacity-20 -translate-y-1/3 translate-x-1/3"
        style={{
          background: `linear-gradient(to bottom right, ${colors.primary}60, ${colors.secondary}60)`
        }}
      />
      <div
        className="absolute bottom-0 left-0 w-96 h-96 rounded-full opacity-20 translate-y-1/2 -translate-x-1/3"
        style={{
          background: `linear-gradient(to top right, ${colors.primary}40, ${colors.secondary}40)`
        }}
      />
      
      {/* Header */}
      <div className="flex items-start justify-between mb-12 relative z-10">
        <div>
          <div className="inline-block">
            <h1
              className="text-6xl font-black tracking-tight mb-4"
              style={{
                background: `linear-gradient(to right, ${colors.primary}, ${colors.secondary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              INVOICE
            </h1>
          </div>
          <div className="flex gap-6 mb-3">
            {data.invoiceNumber && (
              <div className="px-3 py-1 bg-purple-100 rounded-full">
                <span className="text-xs font-medium text-purple-700">No. {data.invoiceNumber}</span>
              </div>
            )}
            {data.invoiceDate && (
              <div className="px-3 py-1 bg-fuchsia-100 rounded-full">
                <span className="text-xs font-medium text-fuchsia-700">Date: {data.invoiceDate}</span>
              </div>
            )}
          </div>
          <div className="mt-4 space-y-1">
            <div
              className="text-base font-semibold"
              style={{
                background: `linear-gradient(to right, ${colors.primary}, ${colors.secondary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              {data.company.companyName}
            </div>
            <div className="text-sm text-gray-600">{data.company.address}</div>
            <div className="text-sm text-gray-600">VAT: {data.company.vat}</div>
          </div>
        </div>
        {data.logo && (
          <div className="w-28 h-28 relative p-2 bg-white rounded-2xl shadow-lg drop-shadow-lg">
            <div
              className="absolute inset-0 rounded-2xl opacity-50"
              style={{
                background: `linear-gradient(to bottom right, ${colors.accent}, ${colors.primary}40)`
              }}
            />
            <Image 
              src={data.logo} 
              alt="Logo" 
              fill 
              className="object-contain p-3 relative z-10" 
              sizes="112px" 
            />
          </div>
        )}
      </div>
      
      {/* Client info */}
      <div className="mb-10 p-6 bg-white/70 backdrop-blur-sm rounded-2xl shadow-sm">
        <div
          className="font-semibold text-sm uppercase tracking-wider mb-3"
          style={{
            background: `linear-gradient(to right, ${colors.primary}, ${colors.secondary})`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}
        >
          Bill To
        </div>
        <div className="font-bold text-lg text-gray-800 mb-2">
          {data.client.companyName || `${data.client.firstName} ${data.client.lastName}`}
        </div>
        <div className="grid grid-cols-2 gap-2">
          <div className="text-sm text-gray-600">{data.client.address}</div>
          <div className="text-sm text-gray-600">VAT: {data.client.vat}</div>
          <div className="text-sm text-gray-600">Email: {data.client.email}</div>
          <div className="text-sm text-gray-600">Phone: {data.client.phone}</div>
        </div>
      </div>
      
      {/* Services table */}
      <div className="mb-10 overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm shadow-sm">
        <table className="w-full divide-y" style={{ borderColor: `${colors.primary}20` }}>
          <thead style={{ background: `linear-gradient(to right, ${colors.accent}80, ${colors.accent}60)` }}>
            <tr>
              <th className="text-left py-4 px-6 text-gray-800 font-semibold">Description</th>
              <th className="text-right py-4 px-6 text-gray-800 font-semibold">Price</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-purple-50">
            {data.services.map((s: Service, i: number) => (
              <tr key={i} className="hover:bg-fuchsia-50/50 transition-colors duration-150">
                <td className="py-3 px-6 text-sm text-gray-700">{s.description}</td>
                <td className="py-3 px-6 text-right text-sm font-medium text-gray-800">
                  {data.currency} {s.price.toFixed(2)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-64 bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
          <div className="flex justify-between items-center text-sm pb-3">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-medium text-gray-800">{data.currency} {subtotal.toFixed(2)}</span>
          </div>
          {data.vatPercent && (
            <div
              className="flex justify-between items-center text-sm py-3 border-b border-dashed"
              style={{ borderColor: `${colors.primary}40` }}
            >
              <span className="text-gray-600">VAT ({data.vatPercent}%)</span>
              <span className="font-medium text-gray-800">{data.currency} {vatAmount.toFixed(2)}</span>
            </div>
          )}
          <div className="flex justify-between items-center font-bold text-lg mt-4 pt-3">
            <span
              style={{
                background: `linear-gradient(to right, ${colors.primary}, ${colors.secondary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              Total
            </span>
            <span
              style={{
                background: `linear-gradient(to right, ${colors.primary}, ${colors.secondary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              {data.currency} {total.toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      {/* Payment Information */}
      {data.paymentInfo && (
        <div
          className="mb-8 p-6 bg-white/60 backdrop-blur-sm rounded-2xl shadow-sm border"
          style={{ borderColor: `${colors.primary}20` }}
        >
          <h3
            className="text-sm font-semibold mb-3"
            style={{
              background: `linear-gradient(to right, ${colors.primary}, ${colors.secondary})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}
          >
            Payment Information
          </h3>
          <div className="text-xs text-gray-600 whitespace-pre-line leading-relaxed">
            {data.paymentInfo}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="text-center text-sm text-gray-500 pt-4 border-t border-purple-100">
        <div className="font-medium">Thank you for your business</div>
        <div className="text-xs mt-1 text-gray-400">{data.company.companyName} • {new Date().getFullYear()}</div>
      </div>
    </div>
  );
}
